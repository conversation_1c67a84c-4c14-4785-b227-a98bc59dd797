import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'dart:math';

// نقطة البداية للتطبيق
void main() {
  runApp(const LetterTracingApp());
}

class LetterTracingApp extends StatelessWidget {
  const LetterTracingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'كاتبي الصغير',
      // نجعل الواجهة تدعم اللغة العربية من اليمين لليسار
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', ''), // '' means no country code
      ],
      locale: const Locale('ar', ''), // اجعل العربية هي اللغة الافتراضية
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Tajawal',
      ),
      home: const LetterTracingScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// الشاشة الرئيسية للتطبيق التي تدير الحالة (النقاط، الحرف الحالي)
class LetterTracingScreen extends StatefulWidget {
  const LetterTracingScreen({super.key});

  @override
  State<LetterTracingScreen> createState() => _LetterTracingScreenState();
}

class _LetterTracingScreenState extends State<LetterTracingScreen> {
  int _score = 0;
  int _currentLetterIndex = 0;
  // GlobalKey لإعادة تعيين حالة الرسم عند الانتقال لحرف جديد
  late GlobalKey<_LetterTracerState> _tracerKey;

  // قائمة الحروف مع نقاط المسار الخاصة بها
  // النقاط هي إحداثيات (x, y) داخل مربع الرسم
  final List<Map<String, dynamic>> _letters = [
    {
      'letter': 'ب',
      'points': [
        const Offset(220, 200),
        const Offset(180, 200),
        const Offset(140, 200),
        const Offset(100, 205),
        const Offset(80, 215),
        const Offset(70, 200),
        const Offset(60, 180),
      ],
      // النقطة الإضافية لحرف الباء
      'dots': [const Offset(150, 240)],
    },
    {
      'letter': 'ا',
      'points': [
        const Offset(150, 50),
        const Offset(150, 90),
        const Offset(150, 130),
        const Offset(150, 170),
        const Offset(150, 210),
        const Offset(150, 250),
      ],
      'dots': [],
    },
    {
      'letter': 'ت',
      'points': [
        const Offset(220, 200),
        const Offset(180, 200),
        const Offset(140, 200),
        const Offset(100, 205),
        const Offset(80, 215),
        const Offset(70, 200),
        const Offset(60, 180),
      ],
      // النقط الإضافية لحرف التاء
      'dots': [const Offset(130, 160), const Offset(170, 160)],
    },
  ];

  @override
  void initState() {
    super.initState();
    _tracerKey = GlobalKey();
  }

  void _onLetterCompleted() {
    // عرض رسالة تشجيعية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('أحسنت! عمل رائع!', textAlign: TextAlign.center, style: TextStyle(fontSize: 20)),
        backgroundColor: Colors.green,
      ),
    );

    // تحديث الحالة لزيادة النقاط والانتقال للحرف التالي
    setState(() {
      _score++;
      // الانتقال للحرف التالي أو العودة للبداية إذا انتهت الحروف
      _currentLetterIndex = (_currentLetterIndex + 1) % _letters.length;
      // إعادة إنشاء مفتاح جديد لإجبار ويدجت الرسم على إعادة البناء بالكامل
      _tracerKey = GlobalKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentLetterData = _letters[_currentLetterIndex];

    return Scaffold(
      appBar: AppBar(
        title: const Text('لعبة تتبع الحروف'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // عرض النقاط
            Text('النقاط: $_score', style: const TextStyle(fontSize: 32, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            // عرض الحرف المطلوب كتابته
            Text('اكتب حرف: "${currentLetterData['letter']}"', style: const TextStyle(fontSize: 40)),
            const SizedBox(height: 20),
            // منطقة الرسم
            Center(
              child: Container(
                width: 300,
                height: 300,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  border: Border.all(color: Colors.blueAccent, width: 2),
                  borderRadius: BorderRadius.circular(12),
                ),
                // ويدجت الرسم المخصص
                child: LetterTracer(
                  key: _tracerKey, // استخدام المفتاح هنا
                  points: currentLetterData['points'],
                  dots: currentLetterData['dots'],
                  onComplete: _onLetterCompleted,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ويدجت الرسم المخصص الذي يتعامل مع اللمس والتحقق
class LetterTracer extends StatefulWidget {
  final List<Offset> points;
  final List<Offset> dots;
  final VoidCallback onComplete;

  const LetterTracer({
    super.key,
    required this.points,
    required this.dots,
    required this.onComplete,
  });

  @override
  State<LetterTracer> createState() => _LetterTracerState();
}

class _LetterTracerState extends State<LetterTracer> {
  // المسار الذي يرسمه المستخدم
  final List<Offset> _userPath = [];
  // مؤشر للنقطة التالية التي يجب على المستخدم الوصول إليها
  int _nextPointIndex = 0;

  void _checkPoint(Offset userPoint) {
    if (_nextPointIndex >= widget.points.length) return;

    final targetPoint = widget.points[_nextPointIndex];
    // حساب المسافة بين إصبع المستخدم والنقطة المستهدفة
    final distance = (userPoint - targetPoint).distance;

    // إذا كان المستخدم قريبًا بما فيه الكفاية من النقطة
    if (distance < 25) { // 25 بكسل هي مسافة التسامح
      setState(() {
        // نضيف النقطة المستهدفة (وليس نقطة المستخدم) لجعل الخط نظيفًا
        _userPath.add(targetPoint);
        _nextPointIndex++;
      });

      // إذا أكمل المستخدم جميع النقاط
      if (_nextPointIndex == widget.points.length) {
        // تأخير بسيط ثم استدعاء دالة الإكمال
        Future.delayed(const Duration(milliseconds: 300), () {
          widget.onComplete();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // GestureDetector لاكتشاف حركة الإصبع على الشاشة
    return GestureDetector(
      onPanStart: (details) {
        _checkPoint(details.localPosition);
      },
      onPanUpdate: (details) {
        _checkPoint(details.localPosition);
      },
      // CustomPaint هو الذي يقوم بالرسم الفعلي
      child: CustomPaint(
        painter: _LetterPainter(
          targetPoints: widget.points,
          userPath: _userPath,
          extraDots: widget.dots,
        ),
        size: const Size(300, 300),
      ),
    );
  }
}

// فئة الرسام المخصص التي ترسم النقاط والخطوط على الشاشة
class _LetterPainter extends CustomPainter {
  final List<Offset> targetPoints;
  final List<Offset> userPath;
  final List<Offset> extraDots;

  _LetterPainter({
    required this.targetPoints,
    required this.userPath,
    required this.extraDots,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 1. إعداد فرشاة الرسم للنقاط المنقطة (الهدف)
    final pointPaint = Paint()
      ..color = Colors.grey.withOpacity(0.5)
      ..style = PaintingStyle.fill;

    // رسم كل النقاط المستهدفة كدوائر صغيرة
    for (var point in targetPoints) {
      canvas.drawCircle(point, 10, pointPaint);
    }

    // رسم النقاط الإضافية (لنقاط الباء والتاء)
    final extraDotPaint = Paint()
      ..color = Colors.blueAccent.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    for (var dot in extraDots) {
        canvas.drawCircle(dot, 15, extraDotPaint);
    }

    // 2. إعداد فرشاة الرسم لخط المستخدم
    final pathPaint = Paint()
      ..color = Colors.blueAccent
      ..strokeWidth = 15
      ..strokeCap = StrokeCap.round // يجعل نهاية الخط دائرية
      ..style = PaintingStyle.stroke;

    // رسم المسار الذي تتبعه المستخدم
    if (userPath.isNotEmpty) {
      final path = Path();
      path.moveTo(userPath.first.dx, userPath.first.dy);
      for (var i = 1; i < userPath.length; i++) {
        path.lineTo(userPath[i].dx, userPath[i].dy);
      }
      canvas.drawPath(path, pathPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true; // يجب إعادة الرسم دائمًا عند تغيير الحالة
  }
}