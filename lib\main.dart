import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// نقطة البداية للتطبيق
void main() {
  runApp(const LetterTracingApp());
}

class LetterTracingApp extends StatelessWidget {
  const LetterTracingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'كاتبي الصغير',
      // نجعل الواجهة تدعم اللغة العربية من اليمين لليسار
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar', ''), // '' means no country code
      ],
      locale: const Locale('ar', ''), // اجعل العربية هي اللغة الافتراضية
      theme: ThemeData(
        primarySwatch: Colors.teal,
        fontFamily: 'Tajawal',
      ),
      home: const LetterTracingScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// الشاشة الرئيسية للتطبيق التي تدير الحالة (النقاط، الحرف الحالي)
class LetterTracingScreen extends StatefulWidget {
  const LetterTracingScreen({super.key});

  @override
  State<LetterTracingScreen> createState() => _LetterTracingScreenState();
}

class _LetterTracingScreenState extends State<LetterTracingScreen> {
  int _score = 0;
  int _currentLetterIndex = 0;
  // GlobalKey لإعادة تعيين حالة الرسم عند الانتقال لحرف جديد
  late GlobalKey<_LetterTracerState> _tracerKey;

  // قائمة الحروف مع بيانات المسار ونقاط التتبع
  final List<Map<String, dynamic>> _letters = [
    {
      'letter': 'ب',
      // المسار الفعلي للحرف - شكل أفقي مع انحناءات طفيفة
      'pathData': [
        {'type': 'move', 'point': const Offset(80, 120)}, // بداية عمودية
        {'type': 'line', 'point': const Offset(80, 150)}, // خط عمودي قصير
        {'type': 'line', 'point': const Offset(220, 150)}, // الخط الأفقي الرئيسي
        {'type': 'line', 'point': const Offset(220, 120)}, // نهاية عمودية
      ],
      // النقاط التي يتتبعها الطفل - 5 نقاط أفقية + نقطتان عموديتان
      'trackingPoints': [
        const Offset(80, 120),   // نقطة عمودية في البداية
        const Offset(80, 150),   // نقطة أفقية 1
        const Offset(115, 150),  // نقطة أفقية 2
        const Offset(150, 150),  // نقطة أفقية 3 (الوسط)
        const Offset(185, 150),  // نقطة أفقية 4
        const Offset(220, 150),  // نقطة أفقية 5
        const Offset(220, 120),  // نقطة عمودية في النهاية
      ],
      // نقطة الباء تحت الخط في الوسط
      'dots': [const Offset(150, 180)],
    },
    {
      'letter': 'ا',
      'pathData': [
        {'type': 'move', 'point': const Offset(150, 50)},
        {'type': 'line', 'point': const Offset(150, 250)},
      ],
      'trackingPoints': [
        const Offset(150, 50),
        const Offset(150, 90),
        const Offset(150, 130),
        const Offset(150, 170),
        const Offset(150, 210),
        const Offset(150, 250),
      ],
      'dots': [],
    },
    {
      'letter': 'ت',
      // نفس مسار حرف الباء
      'pathData': [
        {'type': 'move', 'point': const Offset(80, 120)},
        {'type': 'line', 'point': const Offset(80, 150)},
        {'type': 'line', 'point': const Offset(220, 150)},
        {'type': 'line', 'point': const Offset(220, 120)},
      ],
      // نفس نقاط تتبع حرف الباء
      'trackingPoints': [
        const Offset(80, 120),
        const Offset(80, 150),
        const Offset(115, 150),
        const Offset(150, 150),
        const Offset(185, 150),
        const Offset(220, 150),
        const Offset(220, 120),
      ],
      // نقطتان فوق الخط للتاء
      'dots': [const Offset(130, 90), const Offset(170, 90)],
    },
  ];

  @override
  void initState() {
    super.initState();
    _tracerKey = GlobalKey();
  }

  void _onLetterCompleted() {
    // عرض رسالة تشجيعية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('أحسنت! عمل رائع!', textAlign: TextAlign.center, style: TextStyle(fontSize: 20)),
        backgroundColor: Colors.green,
      ),
    );

    // تحديث الحالة لزيادة النقاط والانتقال للحرف التالي
    setState(() {
      _score++;
      // الانتقال للحرف التالي أو العودة للبداية إذا انتهت الحروف
      _currentLetterIndex = (_currentLetterIndex + 1) % _letters.length;
      // إعادة إنشاء مفتاح جديد لإجبار ويدجت الرسم على إعادة البناء بالكامل
      _tracerKey = GlobalKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    final currentLetterData = _letters[_currentLetterIndex];

    return Scaffold(
      appBar: AppBar(
        title: const Text('لعبة تتبع الحروف'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // عرض النقاط
            Text('النقاط: $_score', style: const TextStyle(fontSize: 32, fontWeight: FontWeight.bold)),
            const SizedBox(height: 20),
            // عرض الحرف المطلوب كتابته
            Text('اكتب حرف: "${currentLetterData['letter']}"', style: const TextStyle(fontSize: 40)),
            const SizedBox(height: 20),
            // منطقة الرسم
            Center(
              child: Container(
                width: 300,
                height: 300,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  border: Border.all(color: Colors.blueAccent, width: 2),
                  borderRadius: BorderRadius.circular(12),
                ),
                // ويدجت الرسم المخصص
                child: LetterTracer(
                  key: _tracerKey,
                  pathData: currentLetterData['pathData'] as List<Map<String, dynamic>>, // جديد
                  trackingPoints: currentLetterData['trackingPoints'] as List<Offset>, // اسم جديد
                  dots: (currentLetterData['dots'] as List).map((d) => d as Offset).toList(),
                  onComplete: _onLetterCompleted,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ويدجت الرسم المخصص الذي يتعامل مع اللمس والتحقق
class LetterTracer extends StatefulWidget {
  final List<Map<String, dynamic>> pathData; // جديد
  final List<Offset> trackingPoints;         // اسم جديد
  final List<Offset> dots;
  final VoidCallback onComplete;

  const LetterTracer({
    super.key,
    required this.pathData,             // جديد
    required this.trackingPoints,      // اسم جديد
    required this.dots,
    required this.onComplete,
  });

  @override
  State<LetterTracer> createState() => _LetterTracerState();
}

class _LetterTracerState extends State<LetterTracer> {
  // المسار الذي يرسمه المستخدم
  final List<Offset> _userPath = [];
  // مؤشر للنقطة التالية التي يجب على المستخدم الوصول إليها
  int _nextPointIndex = 0;
  // النقاط الإضافية المكتملة
  final Set<Offset> _completedDots = {};
  // مؤشر للنقطة الإضافية التالية
  int _nextDotIndex = 0;
  // عدد الأخطاء
  int _errorCount = 0;
  // هل انتهى من الخط الأساسي
  bool _mainPathCompleted = false;

  void _checkInteraction(Offset userPoint) {
    // 1. التحقق من الخروج من الإطار
    if (userPoint.dx < 0 || userPoint.dx > 300 || userPoint.dy < 0 || userPoint.dy > 300) {
      setState(() {
        _errorCount++;
      });
      _showError("خرجت من الإطار! حاول مرة أخرى");
      return;
    }

    // 2. التحقق من اتجاه الكتابة (من اليمين لليسار)
    if (_userPath.isNotEmpty) {
      final lastPoint = _userPath.last;
      if (userPoint.dx > lastPoint.dx + 10) { // إذا تحرك كثيراً لليمين
        setState(() {
          _errorCount++;
        });
        _showError("اكتب من اليمين إلى اليسار!");
        return;
      }
    }

    // 3. التحقق من النقاط الأساسية
    if (!_mainPathCompleted && _nextPointIndex < widget.trackingPoints.length) {
      final targetPoint = widget.trackingPoints[_nextPointIndex];
      final distance = (userPoint - targetPoint).distance;

      if (distance < 25) {
        setState(() {
          _userPath.add(targetPoint);
          _nextPointIndex++;

          // إذا انتهى من الخط الأساسي
          if (_nextPointIndex == widget.trackingPoints.length) {
            _mainPathCompleted = true;
          }
        });
      }
    }

    // 4. التحقق من النقاط الإضافية (النقاط الزرقاء)
    if (_mainPathCompleted && _nextDotIndex < widget.dots.length) {
      final targetDot = widget.dots[_nextDotIndex];
      final distance = (userPoint - targetDot).distance;

      if (distance < 25) {
        setState(() {
          _completedDots.add(targetDot);
          _nextDotIndex++;
        });
      }
    }

    // 5. التحقق من اكتمال الحرف
    if (_mainPathCompleted && _nextDotIndex == widget.dots.length) {
      Future.delayed(const Duration(milliseconds: 300), () {
        widget.onComplete();
      });
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, textAlign: TextAlign.center),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // GestureDetector لاكتشاف حركة الإصبع على الشاشة
    return GestureDetector(
      onPanStart: (details) {
        _checkPoint(details.localPosition);
      },
      onPanUpdate: (details) {
        _checkPoint(details.localPosition);
      },
      // CustomPaint هو الذي يقوم بالرسم الفعلي
      child: CustomPaint(
        painter: _LetterPainter(
          pathData: widget.pathData,
          trackingPoints: widget.trackingPoints,
          userPath: _userPath,
          extraDots: widget.dots,
          completedDots: _completedDots,
        ),
        size: const Size(300, 300),
      ),
    );
  }
}

// فئة الرسام المخصص التي ترسم المنحنيات (النسخة النهائية)
class _LetterPainter extends CustomPainter {
  final List<Map<String, dynamic>> pathData;
  final List<Offset> trackingPoints;
  final List<Offset> userPath;
  final List<Offset> extraDots;
  final Set<Offset> completedDots;

  _LetterPainter({
    required this.pathData,
    required this.trackingPoints,
    required this.userPath,
    required this.extraDots,
    required this.completedDots,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 1. إعداد فرشاة لرسم مسار الحرف الإرشادي (المنحنى الرمادي)
    final guidePathPaint = Paint()
      ..color = Colors.grey.withOpacity(0.4)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 15
      ..strokeCap = StrokeCap.round;

    // بناء المسار المنحني من بيانات pathData
    final guidePath = Path();
    for (var segment in pathData) {
      if (segment['type'] == 'move') {
        guidePath.moveTo(
            (segment['point'] as Offset).dx, (segment['point'] as Offset).dy);
      } else if (segment['type'] == 'line') {
        guidePath.lineTo(
            (segment['point'] as Offset).dx, (segment['point'] as Offset).dy);
      } else if (segment['type'] == 'cubic') {
        guidePath.cubicTo(
          (segment['p1'] as Offset).dx,
          (segment['p1'] as Offset).dy,
          (segment['p2'] as Offset).dx,
          (segment['p2'] as Offset).dy,
          (segment['p3'] as Offset).dx,
          (segment['p3'] as Offset).dy,
        );
      }
    }
    canvas.drawPath(guidePath, guidePathPaint);

    // 2. إعداد فرشاة لرسم نقاط التتبع التي يلمسها الطفل
    final pointPaint = Paint()
      ..color = Colors.grey.withOpacity(0.8)
      ..style = PaintingStyle.fill;

    for (var point in trackingPoints) {
      canvas.drawCircle(point, 10, pointPaint);
    }

    // 3. رسم النقاط الإضافية (لنقاط الباء والتاء)
    // ... (هذا الجزء لا يتغير)
    final extraDotPaint = Paint()
      ..color = Colors.blueAccent.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    final completedDotPaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.fill;
    for (var dot in extraDots) {
      if (completedDots.contains(dot)) {
        canvas.drawCircle(dot, 15, completedDotPaint);
      } else {
        canvas.drawCircle(dot, 15, extraDotPaint);
      }
    }

    // 4. إعداد فرشاة الرسم لخط المستخدم
    final pathPaint = Paint()
      ..color = Colors.blueAccent
      ..strokeWidth = 15
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;

    // رسم المسار الذي تتبعه المستخدم (لا يزال خطوطًا مستقيمة، وهذا جيد)
    if (userPath.isNotEmpty) {
      final path = Path();
      path.moveTo(userPath.first.dx, userPath.first.dy);
      for (var i = 1; i < userPath.length; i++) {
        path.lineTo(userPath[i].dx, userPath[i].dy);
      }
      canvas.drawPath(path, pathPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}