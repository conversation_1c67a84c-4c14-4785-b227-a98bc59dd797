{"net": {"http_server_properties": {"servers": [{"anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "network_stats": {"srtt": 150922}, "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399334877995128", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "network_stats": {"srtt": 111837}, "server": "https://r3---sn-5abxgpxuxaxjvh-vgol.gvt1.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399335205260380", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://beacons.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 45714}, "server": "https://b1.nel.goog", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 33236}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 30176}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 113599}, "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338043131125", "port": 443, "protocol_str": "quic"}], "anonymization": ["LAAAACYAAABodHRwczovL2xlbnNmcm9udGVuZC1wYS5nb29nbGVhcGlzLmNvbQAA", false, 0], "network_stats": {"srtt": 29364}, "server": "https://lensfrontend-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338045415811", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 64229}, "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338048788684", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 227273}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338051359512", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 72868}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338052751477", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 46448}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338056622806", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 65202}, "server": "https://encrypted-tbn3.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338056620089", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 60464}, "server": "https://encrypted-tbn1.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338058453487", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 31806}, "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338063651867", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 38969}, "server": "https://encrypted-tbn2.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338075392009", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 221453}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338078455949", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 86506}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338274313610", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 59276}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338274614100", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABjaHJvbWUtdW50cnVzdGVkOi8vbGVucwA=", true, 0], "network_stats": {"srtt": 40911}, "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399338078337375", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 63560}, "server": "https://www.google.com"}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}