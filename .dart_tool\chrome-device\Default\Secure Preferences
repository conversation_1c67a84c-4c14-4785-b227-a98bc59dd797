{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Découvrez des applications, des jeux, des extensions et des thèmes exceptionnels pour Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\138.0.7204.100\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "4617F0E8C9CD6CAEE908E3F13B8A0CB2239BD98CE147544B019881E2D7E0FA97"}, "extensions": {"ui": {"developer_mode": "DAAB725ECA6D0832DF59620842CBDA8321E101E5236A0008E6B0A2243CBEAC7C"}}, "homepage": "5CEACEF0CEC8019EA5B6BA6521C1F33DF643D36D54981B9A1CB77E3315C1F430", "homepage_is_newtabpage": "A336F558F77DE9B630EC8546CAB0F23A241F3774552B928002C506F0B52BF3AE", "session": {"restore_on_startup": "95B12DEC28A33249F297A8551D765CF55BCD7FE65FB4012B80EB49C7133A51C6", "startup_urls": "958EBF77A22970885B2150FE7BB591E6B9BBF6569878A710EE00E81EA4080CFF"}}, "browser": {"show_home_button": "C03264D209EF2CC624311472B2C8A53A60A796A335DA49D82AE7211521398B2A"}, "default_search_provider_data": {"template_url_data": "C695128828C82C2F29427EF809BD8C92F8CA3D16E6110CA72DD7CB58C41006C1"}, "enterprise_signin": {"policy_recovery_token": "9283EEA78814BEA800AC99DAD9B2E95AEE88D6F626212B399F2589F279E86EA8"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "EEE84C5A5A185D632143DE42F451E87FC43A3F39F23B0426BFA21D4FFD48F264", "fignfifoniblkonapihmkfakmlgkbkcf": "316FDE5076AE92E1D1C55788F1BD4EC5955FE7E14751B27D4D9017356CFCF837", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "F190247AA7BA90A3134974885951CA6F0EAF571ACE8F5D59E3D2F0FD9683550E", "nkeimhogjdpnpccoofpliimaahmaaome": "61481FFD044BAD3E3CBC7625C0442ABEC667D86A9FBECEF1116E4C924BF03653"}, "ui": {"developer_mode": "7D8BDEF44A3CE36ACB75ECFA8978E7844EC6A263BBB20A361EF0BC39445D0950"}}, "google": {"services": {"account_id": "9124AC668326D1C40F1F46E4D088E21F790A787FC42EE22FD7AA2639A081BC07", "last_signed_in_username": "41A7CD077139E3D101B84C729F8A931FF5C7E4D3C00058A6E6AEEE5846CE0F6A", "last_username": "EB37940058CE6EAB00781441FC82F208A8883AB478128C6EFC025D9A1389C8FF"}}, "homepage": "AD0858D3AA33E99299CA3594F0E268D300B18080DC6AC782884A2EEC70E8B9C1", "homepage_is_newtabpage": "39369EE7E07A29BF3C95911BEF4894CD575534FEFD400D46E5D875F8A95F1BE6", "media": {"cdm": {"origin_data": "A7A51D6108AB5066EFC533A8B710D669D7688E293085DC1C635CA790BC4B0689"}, "storage_id_salt": "456D7811B085A19C4C1E644FCB8CDCE712CA1C5CE98E79057D964A992BE79E82"}, "module_blocklist_cache_md5_digest": "C72F95171429FBC76418AE41F604F745F19AB4FF1C892DA8773D58B821EB3EFD", "pinned_tabs": "E010B17C54853272191A9478B6AC99A8EBCBA2C1AA2E4FF6C9F9C54EA75DEAA6", "prefs": {"preference_reset_time": "D0AE02939F0BBABD3C425D3CA0FF4914B6E1352B89D5DED373F9EE8122559EAE"}, "safebrowsing": {"incidents_sent": "13A3147BE5C3DA6590956F89DD7151B6CF991DA6B810C7E847EACB43C01E280C"}, "search_provider_overrides": "C767B6D9A136E71406DEF6E84AC42F83CECCE2767F0E25354503A90DCC38208A", "session": {"restore_on_startup": "9E4CD7EC8B932E2C3E4219F09830963690157D6F8EBB8F08A3600989DF526FA2", "startup_urls": "C2B3B4FD204D0F1066E1DC7ACED757FB44EB5E50703708023FBC93B1BADE2B2F"}}, "super_mac": "BA19D8FAD4FBF3000E21D006F6B76538F8AC35EE079706E7AC1A3B9EE1763EF6"}}